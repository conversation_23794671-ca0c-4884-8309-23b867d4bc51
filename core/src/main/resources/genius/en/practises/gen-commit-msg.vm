Given the below code differences (diffs), please generate a concise, clear, and straight-to-the-point commit message.

- Make sure to prioritize the main action.
- Avoid overly verbose descriptions or unnecessary details.
- Start with a short sentence in imperative form, no more than 50 characters long.
- Then leave an empty line and continue with a more detailed explanation, if necessary.

Follow the Conventional Commits specification, examples:

- fix(authentication): fix password regex pattern case
- feat(storage): add support for S3 storage
- test(java): fix test case for user controller
- docs(architecture): add architecture diagram to home page

#if($context.originText.length() > 0)
User origin intention was: $context.originText
#end

Diff:

```diff
${context.diffContent}
```
